package com.sbg.ug.optimus_backend.billing.orchastration;

import lombok.Getter;

@Getter
public class TransactionResult {
    private final boolean success;
    private final Exception exception;
    private final TransactionContext context;

    private TransactionResult(boolean success, Exception exception, TransactionContext context) {
        this.success = success;
        this.exception = exception;
        this.context = context;
    }

    public static TransactionResult success(TransactionContext context) {
        return new TransactionResult(true, null, context);
    }

    public static TransactionResult failure(Exception exception, TransactionContext context) {
        return new TransactionResult(false, exception, context);
    }
}
